<!-- Plugin Configuration File. Read more: https://plugins.jetbrains.com/docs/intellij/plugin-configuration-file.html -->
<idea-plugin>
    <!-- Unique identifier of the plugin. It should be FQN. It cannot be changed between the plugin versions. -->
    <id>cn.leo.helper.YTOAIKpiHelper</id>

    <!-- Public plugin name should be written in Title Case.
         Guidelines: https://plugins.jetbrains.com/docs/marketplace/plugin-overview-page.html#plugin-name -->
    <name>YTOAIKpiHelper</name>

    <!-- A displayed Vendor name or Organization ID displayed on the Plugins Page. -->
    <vendor>Leo</vendor>

    <!-- Description of the plugin displayed on the Plugin Page and IDE Plugin Manager.
         Guidelines: https://plugins.jetbrains.com/docs/marketplace/plugin-overview-page.html#plugin-description -->
    <description><![CDATA[
    <h3>YTO AI助手自动化插件</h3>
    <p>专为YTO AI插件(YtoAICode)设计的自动化助手，帮助提升开发效率。</p>

    <h4>🚀 主要功能</h4>
    <ul>
        <li><strong>智能调度</strong>：自动在指定时间间隔执行AI交互</li>
        <li><strong>多种操作</strong>：支持聊天、代码解释、Bug查找、测试生成等</li>
        <li><strong>问题生成</strong>：内置200+种编程相关问题模板</li>
        <li><strong>工作时间限制</strong>：可设置仅在工作时间执行</li>
        <li><strong>统计监控</strong>：记录执行次数和成功率</li>
    </ul>

    <h4>📖 快速使用</h4>
    <ol>
        <li>确保已安装YTO AI插件(YtoAICode)</li>
        <li>通过菜单 <strong>Tools → AI助手自动化 → 配置设置</strong> 进行配置</li>
        <li>设置执行间隔时间(秒)和自定义Actions</li>
        <li>点击 <strong>启用/禁用自动化</strong> 开启功能</li>
        <li>使用 <strong>立即触发一次</strong> 测试功能</li>
    </ol>

    <h4>⚙️ 配置说明</h4>
    <ul>
        <li><strong>执行间隔</strong>：设置最小和最大执行间隔(秒)，系统会在此范围内随机选择</li>
        <li><strong>自定义Actions</strong>：配置要执行的YTO AI功能，如YtoAICode.NewChat、codegpt.Explain等</li>
        <li><strong>工作时间</strong>：可限制仅在指定时间段和工作日执行</li>
        <li><strong>聊天设置</strong>：支持聊天后自动关闭对话框(Mac使用Shift+ESC)</li>
    </ul>

    <h4>🎯 支持的YTO AI功能</h4>
    <ul>
        <li>YtoAICode.NewChat - 新建聊天</li>
        <li>YtoAICode.AskQuestion - 询问问题</li>
        <li>codegpt.Explain - 解释代码(需选中代码)</li>
        <li>codegpt.FindBugs - 查找Bug(需选中代码)</li>
        <li>codegpt.WriteTests - 生成测试(需选中代码)</li>
        <li>codegpt.Refactor - 重构代码(需选中代码)</li>
        <li>codegpt.Optimize - 优化代码(需选中代码)</li>
    </ul>

    <p><em>注意：本插件仅供学习和测试使用，请确保遵守相关使用条款。</em></p>
  ]]></description>

    <!-- Product and plugin compatibility requirements.
         Read more: https://plugins.jetbrains.com/docs/intellij/plugin-compatibility.html -->
    <depends>com.intellij.modules.platform</depends>

    <applicationListeners>
        <listener class="cn.leo.helper.PluginStartupListener"
                  topic="com.intellij.openapi.project.ProjectManagerListener"/>
    </applicationListeners>

    <!-- Extension points defined by the plugin.
         Read more: https://plugins.jetbrains.com/docs/intellij/plugin-extension-points.html -->
    <extensions defaultExtensionNs="com.intellij">
        <!-- 注册配置服务 -->
        <applicationService serviceImplementation="cn.leo.helper.KpiHelperConfig"/>
    </extensions>

    <!-- Actions -->
    <actions>
        <group id="KpiHelper.Menu" text="AI助手自动化" description="AI助手自动化工具">
            <add-to-group group-id="ToolsMenu" anchor="last"/>

            <action id="KpiHelper.ToggleAction"
                    class="cn.leo.helper.actions.ToggleKpiHelperAction"
                    text="启用/禁用自动化"
                    description="启用或禁用AI助手自动化功能"/>

            <action id="KpiHelper.ConfigAction"
                    class="cn.leo.helper.actions.OpenConfigAction"
                    text="配置设置"
                    description="打开配置设置对话框"/>

            <action id="KpiHelper.StatusAction"
                    class="cn.leo.helper.actions.ShowStatusAction"
                    text="查看状态"
                    description="查看自动化运行状态和统计信息"/>

            <action id="KpiHelper.FindYTOAIAction"
                    class="cn.leo.helper.actions.FindYTOAIActionAction"
                    text="查找YTO AI插件"
                    description="自动查找YTO AI插件的Action ID"/>

            <action id="KpiHelper.TestToolWindowAction"
                    class="cn.leo.helper.actions.TestToolWindowAction"
                    text="测试Tool Window交互"
                    description="测试与YTO AI Tool Window的交互功能"/>
        </group>
    </actions>
</idea-plugin>

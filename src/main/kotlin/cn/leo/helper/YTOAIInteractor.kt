package cn.leo.helper

import com.intellij.openapi.actionSystem.ActionManager
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.actionSystem.impl.SimpleDataContext
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.diagnostic.Logger
import com.intellij.openapi.project.Project
import com.intellij.openapi.wm.ToolWindowManager
import java.awt.Component
import java.awt.Robot
import java.awt.event.KeyEvent
import java.awt.Toolkit
import java.awt.datatransfer.StringSelection
import com.intellij.openapi.util.SystemInfo
import javax.swing.JButton
import javax.swing.JComponent
import javax.swing.text.JTextComponent

/**
 * YTO AI插件交互器
 * 专门负责与YTO AI插件进行交互
 */
class YTOAIInteractor(private val project: Project) {
    
    private val logger = Logger.getInstance(YTOAIInteractor::class.java)
    private val config = KpiHelperConfig.getInstance()
    
    /**
     * 执行与YTO AI插件的交互
     */
    fun executeInteraction(question: String): Boolean {
        logger.info("KpiHelper: 开始与YTO AI插件交互，问题: $question")

        // 策略1: 优先尝试通过Tool Window触发（最可靠）
        if (tryToolWindowApproach(question)) {
            logger.info("KpiHelper: 通过Tool Window成功触发YTO AI插件")
            return true
        }

        // 策略2: 尝试通过Action触发
        if (tryActionApproach(question)) {
            logger.info("KpiHelper: 通过Action成功触发YTO AI插件")
            return true
        }

        // 策略3: 尝试通过键盘快捷键触发
        if (tryKeyboardShortcut(question)) {
            logger.info("KpiHelper: 通过键盘快捷键成功触发YTO AI插件")
            return true
        }

        logger.warn("KpiHelper: 所有方法都失败，无法触发YTO AI插件")
        return false
    }
    
    /**
     * 策略1: 通过Action触发
     */
    private fun tryActionApproach(question: String): Boolean {
        return try {
            val helper = YTOAIPluginHelper()
            val actionId = helper.findYTOAIActionId()

            if (actionId != null) {
                executeAction(actionId, question)
                true
            } else {
                logger.warn("KpiHelper: 未找到YTO AI插件的Action ID")
                false
            }
        } catch (e: Exception) {
            logger.error("KpiHelper: Action方式失败", e)
            false
        }
    }

    /**
     * 直接执行指定的Action ID
     */
    fun executeSpecificAction(actionId: String, question: String? = null): Boolean {
        return try {
            logger.info("KpiHelper: 执行指定Action: $actionId")

            val success = when {
                isChatAction(actionId) -> {
                    logger.info("KpiHelper: 识别为聊天Action: $actionId")
                    executeChatAction(actionId, question)
                }
                isCodeProcessingAction(actionId) -> {
                    logger.info("KpiHelper: 识别为代码处理Action: $actionId")
                    executeCodeProcessingAction(actionId)
                }
                else -> {
                    logger.info("KpiHelper: 识别为通用Action: $actionId")
                    executeActionWithoutInput(actionId)
                    true // executeActionWithoutInput没有返回值，默认返回true
                }
            }

            logger.info("KpiHelper: Action执行结果: $success")
            success
        } catch (e: Exception) {
            logger.error("KpiHelper: 执行指定Action失败: $actionId", e)
            false
        }
    }

    /**
     * 执行聊天Action
     * 修复：避免重复执行codegpt.创建新对话，确保问题输入到正确位置
     */
    private fun executeChatAction(actionId: String, question: String?): Boolean {
        return try {
            logger.info("KpiHelper: 开始执行聊天Action: $actionId")

            when (actionId) {
                "YtoAICode.NewChat" -> {
                    // 只打开聊天窗口，不执行创建新对话
                    executeYtoAINewChat(question)
                }
                "codegpt.创建新对话" -> {
                    // 直接执行创建新对话
                    executeCreateNewChatAction(question)
                }
                else -> {
                    // 其他聊天Action的处理
                    executeGenericChatAction(actionId, question)
                }
            }
            true
        } catch (e: Exception) {
            logger.error("KpiHelper: 执行聊天Action失败: $actionId", e)
            false
        }
    }

    /**
     * 执行YtoAICode.NewChat（打开聊天窗口 → 清除窗口 → 输入问题）
     */
    private fun executeYtoAINewChat(question: String?) {
        val actionManager = ActionManager.getInstance()
        val action = actionManager.getAction("YtoAICode.NewChat")

        if (action != null) {
            val dataContext = SimpleDataContext.getProjectContext(project)
            val event = AnActionEvent.createFromAnAction(action, null, "KpiHelper.YtoAINewChat", dataContext)
            action.actionPerformed(event)
            logger.info("KpiHelper: 成功打开YtoAICode聊天窗口")

            // 延迟执行清除窗口和输入问题
            if (!question.isNullOrEmpty() && config.simulateUserInput) {
                ApplicationManager.getApplication().executeOnPooledThread {
                    try {
                        Thread.sleep(2000L) // 等待聊天窗口加载

                        // 先执行清除窗口，让光标定位到对话框
                        executeCleanupAction()
                        Thread.sleep(1000L) // 等待清除完成

                        // 然后输入问题
                        inputQuestionToChatWindow(question)
                    } catch (e: Exception) {
                        logger.error("KpiHelper: 聊天流程执行失败", e)
                    }
                }
            }
        } else {
            logger.error("KpiHelper: 未找到YtoAICode.NewChat Action")
        }
    }

    /**
     * 执行codegpt.创建新对话
     */
    private fun executeCreateNewChatAction(question: String?) {
        ApplicationManager.getApplication().invokeLater {
            try {
                val actionManager = ActionManager.getInstance()
                val createNewChatAction = actionManager.getAction("codegpt.创建新对话")

                if (createNewChatAction != null) {
                    val dataContext = SimpleDataContext.getProjectContext(project)
                    val event = AnActionEvent.createFromAnAction(createNewChatAction, null, "KpiHelper.CreateNewChat", dataContext)
                    createNewChatAction.actionPerformed(event)
                    logger.info("KpiHelper: 成功执行codegpt.创建新对话")

                    // 延迟输入问题到聊天窗口
                    if (!question.isNullOrEmpty() && config.simulateUserInput) {
                        ApplicationManager.getApplication().executeOnPooledThread {
                            try {
                                Thread.sleep(config.inputDelaySeconds * 1000L + 2000L) // 等待聊天界面完全加载
                                inputQuestionToChatWindow(question)
                            } catch (e: Exception) {
                                logger.warn("KpiHelper: 输入问题到聊天窗口失败", e)
                            }
                        }
                    }
                } else {
                    logger.error("KpiHelper: 未找到codegpt.创建新对话 Action")
                }

            } catch (e: Exception) {
                logger.error("KpiHelper: 执行codegpt.创建新对话失败", e)
            }
        }
    }

    /**
     * 聊天窗口输入方法（修复中文输入问题）
     * YtoAICode.NewChat → 打开聊天窗口 → Tab导航到输入框 → 粘贴问题 → 回车
     */
    private fun inputQuestionToChatWindow(question: String): Boolean {
        return try {
            logger.info("KpiHelper: 开始向聊天窗口输入问题: $question")

            // 将问题复制到剪贴板（支持中文）
            val clipboard = Toolkit.getDefaultToolkit().systemClipboard
            val selection = StringSelection(question)
            clipboard.setContents(selection, null)

            val robot = Robot()
            Thread.sleep(1500) // 等待聊天窗口完全加载

            // 使用Tab键导航到聊天输入框（只按一次）
            logger.info("KpiHelper: 使用Tab键导航到输入框")
            robot.keyPress(KeyEvent.VK_TAB)
            robot.keyRelease(KeyEvent.VK_TAB)
            Thread.sleep(500)  // 等待焦点切换

            Thread.sleep(500)

            // 确保输入框获得焦点后再粘贴
            val modifierKey = if (SystemInfo.isMac) KeyEvent.VK_META else KeyEvent.VK_CONTROL

            // 粘贴内容（支持中文）
            robot.keyPress(modifierKey)
            robot.keyPress(KeyEvent.VK_V)
            Thread.sleep(100)
            robot.keyRelease(KeyEvent.VK_V)
            robot.keyRelease(modifierKey)

            Thread.sleep(500)

            // 按Enter发送
            robot.keyPress(KeyEvent.VK_ENTER)
            robot.keyRelease(KeyEvent.VK_ENTER)

            logger.info("KpiHelper: 成功向聊天窗口输入问题: $question")

            // 如果配置了自动关闭对话框，则等待指定时间后关闭（适用于所有聊天Action）
            scheduleAutoCloseDialog()

            true

        } catch (e: Exception) {
            logger.error("KpiHelper: 向聊天窗口输入问题失败", e)
            false
        }
    }

    /**
     * 执行通用聊天Action
     */
    private fun executeGenericChatAction(actionId: String, question: String?) {
        val actionManager = ActionManager.getInstance()
        val action = actionManager.getAction(actionId)

        if (action == null) {
            throw IllegalArgumentException("聊天Action '$actionId' 未找到")
        }

        val dataContext = SimpleDataContext.getProjectContext(project)
        val event = AnActionEvent.createFromAnAction(action, null, "KpiHelper.GenericChatAction", dataContext)

        action.actionPerformed(event)
        logger.info("KpiHelper: 成功执行通用聊天Action: $actionId")

        // 延迟输入问题
        if (!question.isNullOrEmpty() && config.simulateUserInput) {
            ApplicationManager.getApplication().executeOnPooledThread {
                try {
                    Thread.sleep(config.inputDelaySeconds * 1000L + 1000L)
                    inputQuestionViaClipboard(question)
                } catch (e: Exception) {
                    logger.warn("KpiHelper: 输入问题失败", e)
                }
            }
        }
    }

    /**
     * 执行代码处理Action（直接执行，无需右键菜单）
     * 修复：使用直接执行方式，避免复杂的右键菜单导航
     */
    private fun executeCodeProcessingAction(actionId: String): Boolean {
        return try {
            logger.info("KpiHelper: 开始执行代码处理Action: $actionId")

            // 确保焦点在代码编辑器中并选中代码
            ensureFocusOnEditorAndSelectCode()

            Thread.sleep(1000) // 等待选中完成

            // 直接执行Action
            val success = executeActionDirectly(actionId)

            if (success) {
                logger.info("KpiHelper: 成功执行代码处理Action: $actionId")
                // 为代码处理Action添加延迟关闭功能（清除窗口 → 关闭对话框）
                scheduleDelayedCloseForCodeAction()
            }

            success

        } catch (e: Exception) {
            logger.error("KpiHelper: 执行代码处理Action失败: $actionId", e)
            false
        }
    }

    /**
     * 直接执行Action（无需菜单导航）
     * 修复：为代码处理Action提供正确的DataContext，同步执行并返回结果
     */
    private fun executeActionDirectly(actionId: String): Boolean {
        return try {
            logger.info("KpiHelper: 直接执行Action: $actionId")

            val actionManager = ActionManager.getInstance()
            val action = actionManager.getAction(actionId)

            if (action == null) {
                logger.error("KpiHelper: Action '$actionId' 未找到")
                return false
            }

            // 获取当前编辑器的DataContext
            val dataContext = getCurrentEditorDataContext()
            if (dataContext == null) {
                logger.error("KpiHelper: 无法获取编辑器DataContext")
                return false
            }

            // 尝试使用ActionManager直接执行，避免AnActionEvent相关问题
            var success = false
            ApplicationManager.getApplication().invokeAndWait {
                try {
                    logger.info("KpiHelper: 使用ActionManager直接执行Action '$actionId'")

                    // 方法1：尝试直接通过ActionManager执行
                    val actionManager = ActionManager.getInstance()
                    actionManager.tryToExecute(action, null, null, null, true)

                    logger.info("KpiHelper: 成功执行Action: $actionId")
                    success = true
                } catch (e: Exception) {
                    logger.warn("KpiHelper: ActionManager执行失败，尝试传统方法: ${e.message}")

                    // 方法2：如果失败，使用传统方法但捕获异常
                    try {
                        val event = AnActionEvent.createFromAnAction(action, null, "KpiHelper.DirectCodeAction", dataContext)
                        action.actionPerformed(event)
                        logger.info("KpiHelper: 传统方法执行成功: $actionId")
                        success = true
                    } catch (e2: Exception) {
                        logger.error("KpiHelper: Action '$actionId' 执行失败", e2)
                        success = false
                    }
                }
            }

            success

        } catch (e: Exception) {
            logger.error("KpiHelper: 直接执行Action失败: $actionId", e)
            false
        }
    }

    /**
     * 获取当前编辑器的DataContext
     */
    private fun getCurrentEditorDataContext(): com.intellij.openapi.actionSystem.DataContext? {
        return try {
            // 检查项目是否已初始化
            if (project.isDisposed) {
                logger.warn("KpiHelper: 项目已关闭")
                return SimpleDataContext.getProjectContext(project)
            }

            val fileEditorManager = com.intellij.openapi.fileEditor.FileEditorManager.getInstance(project)
            val selectedEditor = fileEditorManager.selectedEditor

            if (selectedEditor != null) {
                logger.info("KpiHelper: 找到编辑器: ${selectedEditor.file?.name}")
                // 创建包含编辑器信息的DataContext
                val dataContextBuilder = SimpleDataContext.builder()
                dataContextBuilder.add(com.intellij.openapi.actionSystem.CommonDataKeys.PROJECT, project)

                // 安全地获取编辑器
                val editor = if (selectedEditor is com.intellij.openapi.fileEditor.TextEditor) {
                    selectedEditor.editor
                } else {
                    null
                }

                if (editor != null) {
                    dataContextBuilder.add(com.intellij.openapi.actionSystem.CommonDataKeys.EDITOR, editor)
                }

                dataContextBuilder.add(com.intellij.openapi.actionSystem.CommonDataKeys.VIRTUAL_FILE, selectedEditor.file)
                dataContextBuilder.build()
            } else {
                logger.warn("KpiHelper: 没有打开的编辑器")
                SimpleDataContext.getProjectContext(project)
            }
        } catch (e: Exception) {
            logger.error("KpiHelper: 获取编辑器DataContext失败", e)
            SimpleDataContext.getProjectContext(project)
        }
    }

    /**
     * 测试Action是否存在和可用
     */
    fun testActionAvailability(actionId: String): String {
        return try {
            val actionManager = ActionManager.getInstance()
            val action = actionManager.getAction(actionId)

            if (action == null) {
                return "❌ Action '$actionId' 不存在"
            }

            val dataContext = getCurrentEditorDataContext() ?: SimpleDataContext.getProjectContext(project)
            val event = AnActionEvent.createFromAnAction(action, null, "KpiHelper.Test", dataContext)

            action.update(event)

            buildString {
                appendLine("✅ Action '$actionId' 存在")
                appendLine("   - 可用: ${event.presentation.isEnabled}")
                appendLine("   - 可见: ${event.presentation.isVisible}")
                appendLine("   - 文本: ${event.presentation.text ?: "无"}")
                appendLine("   - 描述: ${event.presentation.description ?: "无"}")
                appendLine("   - Action类: ${action.javaClass.simpleName}")
            }
        } catch (e: Exception) {
            "❌ 测试Action '$actionId' 时发生错误: ${e.message}"
        }
    }

    /**
     * 确保焦点在代码编辑器中并选中所有代码
     */
    private fun ensureFocusOnEditorAndSelectCode() {
        try {
            logger.info("KpiHelper: 确保焦点在代码编辑器中并选中代码")
            val robot = Robot()

            // 按Escape键关闭可能打开的对话框或工具窗口
            robot.keyPress(KeyEvent.VK_ESCAPE)
            robot.keyRelease(KeyEvent.VK_ESCAPE)
            Thread.sleep(200)

            // 点击编辑器区域（屏幕中央偏上）
            val screenSize = Toolkit.getDefaultToolkit().screenSize
            val editorX = screenSize.width / 2
            val editorY = screenSize.height / 3 // 编辑器通常在屏幕上方

            robot.mouseMove(editorX, editorY)
            robot.mousePress(java.awt.event.InputEvent.BUTTON1_DOWN_MASK)
            robot.mouseRelease(java.awt.event.InputEvent.BUTTON1_DOWN_MASK)
            Thread.sleep(500)

            // 选中所有代码
            val modifierKey = if (SystemInfo.isMac) KeyEvent.VK_META else KeyEvent.VK_CONTROL
            logger.info("KpiHelper: 选中所有代码，使用${if (SystemInfo.isMac) "Command" else "Control"}+A")

            robot.keyPress(modifierKey)
            robot.keyPress(KeyEvent.VK_A)
            Thread.sleep(100)
            robot.keyRelease(KeyEvent.VK_A)
            robot.keyRelease(modifierKey)

            Thread.sleep(300) // 等待选中完成

        } catch (e: Exception) {
            logger.warn("KpiHelper: 确保编辑器焦点和选中代码失败", e)
        }
    }

    // 已删除复杂的右键菜单导航方法，改为直接执行Action

    /**
     * 判断Action是否需要额外输入
     */
    private fun isActionRequiresInput(actionId: String): Boolean {
        val inputRequiredActions = listOf(
            "YtoAICode.NewChat",
            "YtoAICode.AskQuestion",
            "codegpt.创建新对话"
        )
        return inputRequiredActions.any { actionId.equals(it, ignoreCase = true) }
    }

    /**
     * 判断Action是否是代码处理功能（需要先选中代码，然后右击选择）
     */
    private fun isCodeProcessingAction(actionId: String): Boolean {
        val codeProcessingActions = listOf(
            "codegpt.FindBugs",
            "codegpt.WriteTests",
            "codegpt.Explain",
            "codegpt.Refactor",
            "codegpt.Optimize"
        )
        return codeProcessingActions.any { actionId.equals(it, ignoreCase = true) }
    }

    /**
     * 判断是否是聊天对话Action
     */
    private fun isChatAction(actionId: String): Boolean {
        val chatActions = listOf(
            "codegpt.创建新对话",
            "YtoAICode.NewChat"
        )
        return chatActions.any { actionId.equals(it, ignoreCase = true) }
    }

    /**
     * 执行不需要额外输入的Action（如代码分析功能）
     */
    private fun executeActionWithoutInput(actionId: String) {
        val actionManager = ActionManager.getInstance()
        val action = actionManager.getAction(actionId)

        if (action == null) {
            throw IllegalArgumentException("Action '$actionId' 未找到")
        }

        ApplicationManager.getApplication().invokeLater {
            try {
                val dataContext = SimpleDataContext.getProjectContext(project)
                val event = AnActionEvent.createFromAnAction(action, null, "KpiHelper.DirectAction", dataContext)

                action.actionPerformed(event)
                logger.info("KpiHelper: 成功执行Action（无需输入）: $actionId")

            } catch (e: Exception) {
                logger.error("KpiHelper: 执行Action失败: $actionId", e)
                throw e
            }
        }
    }
    
    /**
     * 策略1: 通过Tool Window触发（优先策略）
     */
    private fun tryToolWindowApproach(question: String): Boolean {
        return try {
            logger.info("KpiHelper: 尝试通过Tool Window激活YTO AI插件")

            val toolWindowManager = ToolWindowManager.getInstance(project)
            val toolWindow = toolWindowManager.getToolWindow("YtoAICode")

            if (toolWindow == null) {
                logger.warn("KpiHelper: 未找到YtoAICode Tool Window，请确保YTO AI插件已安装")
                return false
            }

            if (!toolWindow.isAvailable) {
                logger.warn("KpiHelper: YtoAICode Tool Window不可用")
                return false
            }

            // 激活Tool Window
            var success = false
            val latch = java.util.concurrent.CountDownLatch(1)

            ApplicationManager.getApplication().invokeLater {
                try {
                    // 显示Tool Window
                    toolWindow.show {
                        logger.info("KpiHelper: YtoAICode Tool Window已显示")

                        // 激活Tool Window
                        toolWindow.activate {
                            logger.info("KpiHelper: YtoAICode Tool Window已激活")

                            // 延迟执行交互，让界面完全加载
                            ApplicationManager.getApplication().executeOnPooledThread {
                                try {
                                    Thread.sleep(config.inputDelaySeconds * 1000L + 1000L) // 额外1秒等待

                                    // 尝试直接输入问题
                                    success = directInputToToolWindow(question)

                                    if (!success) {
                                        // 如果直接输入失败，尝试组件交互
                                        success = interactWithToolWindow(toolWindow, question)
                                    }

                                } catch (e: Exception) {
                                    logger.error("KpiHelper: Tool Window交互失败", e)
                                } finally {
                                    latch.countDown()
                                }
                            }
                        }
                    }
                } catch (e: Exception) {
                    logger.error("KpiHelper: 激活Tool Window失败", e)
                    latch.countDown()
                }
            }

            // 等待交互完成
            latch.await(15, java.util.concurrent.TimeUnit.SECONDS)
            success

        } catch (e: Exception) {
            logger.error("KpiHelper: Tool Window方式失败", e)
            false
        }
    }

    /**
     * 直接输入到Tool Window（使用剪贴板和键盘）
     */
    private fun directInputToToolWindow(question: String): Boolean {
        return try {
            logger.info("KpiHelper: 尝试直接输入到Tool Window")

            // 等待一下确保Tool Window获得焦点
            Thread.sleep(1000)

            // 使用剪贴板输入问题
            if (inputQuestionViaClipboard(question)) {
                logger.info("KpiHelper: 成功通过剪贴板输入问题到Tool Window")
                return true
            }

            false
        } catch (e: Exception) {
            logger.error("KpiHelper: 直接输入到Tool Window失败", e)
            false
        }
    }
    
    /**
     * 策略3: 通过键盘快捷键触发
     */
    private fun tryKeyboardShortcut(question: String): Boolean {
        return try {
            logger.info("KpiHelper: 尝试通过键盘快捷键触发YTO AI插件")

            val robot = Robot()
            val modifierKey = if (SystemInfo.isMac) KeyEvent.VK_META else KeyEvent.VK_CONTROL

            // 常见的AI助手快捷键组合（根据系统调整）
            val shortcuts = if (SystemInfo.isMac) {
                listOf(
                    listOf(KeyEvent.VK_META, KeyEvent.VK_SHIFT, KeyEvent.VK_A), // Cmd+Shift+A
                    listOf(KeyEvent.VK_ALT, KeyEvent.VK_A), // Alt+A
                    listOf(KeyEvent.VK_META, KeyEvent.VK_ALT, KeyEvent.VK_A), // Cmd+Alt+A
                )
            } else {
                listOf(
                    listOf(KeyEvent.VK_CONTROL, KeyEvent.VK_SHIFT, KeyEvent.VK_A), // Ctrl+Shift+A
                    listOf(KeyEvent.VK_ALT, KeyEvent.VK_A), // Alt+A
                    listOf(KeyEvent.VK_CONTROL, KeyEvent.VK_ALT, KeyEvent.VK_A), // Ctrl+Alt+A
                )
            }

            for (shortcut in shortcuts) {
                try {
                    val keyNames = shortcut.map { keyCode ->
                        when (keyCode) {
                            KeyEvent.VK_META -> "Cmd"
                            KeyEvent.VK_CONTROL -> "Ctrl"
                            KeyEvent.VK_SHIFT -> "Shift"
                            KeyEvent.VK_ALT -> "Alt"
                            KeyEvent.VK_A -> "A"
                            else -> keyCode.toString()
                        }
                    }
                    logger.info("KpiHelper: 尝试快捷键: ${keyNames.joinToString("+")}")

                    // 按下快捷键
                    shortcut.forEach { robot.keyPress(it) }
                    Thread.sleep(100)
                    shortcut.reversed().forEach { robot.keyRelease(it) }

                    Thread.sleep(1000) // 等待界面响应

                    // 尝试输入问题
                    if (inputQuestionViaClipboard(question)) {
                        return true
                    }

                } catch (e: Exception) {
                    logger.warn("KpiHelper: 快捷键失败", e)
                }
            }

            false
        } catch (e: Exception) {
            logger.error("KpiHelper: 键盘快捷键方式失败", e)
            false
        }
    }
    
    /**
     * 执行指定的Action
     */
    private fun executeAction(actionId: String, question: String) {
        val actionManager = ActionManager.getInstance()
        val action = actionManager.getAction(actionId)
        
        if (action == null) {
            throw IllegalArgumentException("Action '$actionId' 未找到")
        }
        
        ApplicationManager.getApplication().invokeLater {
            try {
                val dataContext = SimpleDataContext.getProjectContext(project)
                val event = AnActionEvent.createFromAnAction(action, null, "KpiHelper.YTOInteraction", dataContext)
                
                action.actionPerformed(event)
                logger.info("KpiHelper: 成功执行Action: $actionId")
                
                // 延迟输入问题
                if (config.simulateUserInput) {
                    ApplicationManager.getApplication().executeOnPooledThread {
                        try {
                            Thread.sleep(config.inputDelaySeconds * 1000L)
                            inputQuestionViaClipboard(question)
                        } catch (e: Exception) {
                            logger.warn("KpiHelper: 输入问题失败", e)
                        }
                    }
                }
                
            } catch (e: Exception) {
                logger.error("KpiHelper: 执行Action失败: $actionId", e)
                throw e
            }
        }
    }
    
    /**
     * 与Tool Window进行交互
     */
    private fun interactWithToolWindow(toolWindow: com.intellij.openapi.wm.ToolWindow, question: String): Boolean {
        return try {
            val contentManager = toolWindow.contentManager
            val content = contentManager.selectedContent
            
            if (content != null) {
                val component = content.component
                logger.info("KpiHelper: 找到Tool Window内容组件: ${component.javaClass.simpleName}")
                
                // 查找并与组件交互
                val inputFound = findInputComponent(component, question)
                val buttonFound = findSubmitButton(component)
                
                inputFound || buttonFound
            } else {
                logger.warn("KpiHelper: Tool Window没有内容")
                false
            }
            
        } catch (e: Exception) {
            logger.error("KpiHelper: Tool Window交互失败", e)
            false
        }
    }
    
    /**
     * 查找并操作输入组件
     */
    private fun findInputComponent(component: Component, question: String): Boolean {
        var found = false
        
        try {
            when (component) {
                is JTextComponent -> {
                    logger.info("KpiHelper: 找到文本输入组件")
                    ApplicationManager.getApplication().invokeLater {
                        try {
                            component.text = question
                            logger.info("KpiHelper: 已输入问题: $question")
                            found = true
                        } catch (e: Exception) {
                            logger.error("KpiHelper: 输入问题失败", e)
                        }
                    }
                }
                is JComponent -> {
                    for (i in 0 until component.componentCount) {
                        if (findInputComponent(component.getComponent(i), question)) {
                            found = true
                        }
                    }
                }
            }
        } catch (e: Exception) {
            logger.error("KpiHelper: 查找输入组件时发生错误", e)
        }
        
        return found
    }
    
    /**
     * 查找并点击提交按钮
     */
    private fun findSubmitButton(component: Component): Boolean {
        var found = false
        
        try {
            when (component) {
                is JButton -> {
                    val buttonText = component.text?.lowercase() ?: ""
                    val isSubmitButton = buttonText.contains("发送") || 
                                       buttonText.contains("send") || 
                                       buttonText.contains("submit") ||
                                       buttonText.contains("ask") ||
                                       buttonText.contains("提交")
                    
                    if (isSubmitButton) {
                        logger.info("KpiHelper: 找到发送按钮: ${component.text}")
                        ApplicationManager.getApplication().invokeLater {
                            try {
                                component.doClick()
                                logger.info("KpiHelper: 已点击发送按钮")
                                found = true
                            } catch (e: Exception) {
                                logger.error("KpiHelper: 点击发送按钮失败", e)
                            }
                        }
                    }
                }
                is JComponent -> {
                    for (i in 0 until component.componentCount) {
                        if (findSubmitButton(component.getComponent(i))) {
                            found = true
                        }
                    }
                }
            }
        } catch (e: Exception) {
            logger.error("KpiHelper: 查找提交按钮时发生错误", e)
        }
        
        return found
    }
    
    /**
     * 通过剪贴板输入问题（已弃用，使用inputQuestionToChatWindow代替）
     * 这个方法有问题，会影响代码编辑器
     */
    @Deprecated("使用inputQuestionToChatWindow代替")
    private fun inputQuestionViaClipboard(question: String): Boolean {
        logger.warn("KpiHelper: inputQuestionViaClipboard已弃用，请使用inputQuestionToChatWindow")
        return inputQuestionToChatWindow(question)
    }

    /**
     * 为代码Action安排延迟关闭（等待延迟时间 → 清除窗口 → 等1秒 → 关闭窗口）
     */
    private fun scheduleDelayedCloseForCodeAction() {
        if (config.autoCloseDialogAfterChat) {
            logger.info("KpiHelper: 安排代码Action的延迟关闭，延迟${config.dialogCloseDelaySeconds}秒")
            ApplicationManager.getApplication().executeOnPooledThread {
                try {
                    // 等待配置的延迟时间
                    Thread.sleep(config.dialogCloseDelaySeconds * 1000L)

                    // 执行清除窗口
                    ApplicationManager.getApplication().invokeLater {
                        executeCleanupAction()
                    }

                    // 等待1秒后关闭窗口
                    Thread.sleep(1000L)
                    ApplicationManager.getApplication().invokeLater {
                        closeDialogWithShiftEsc()
                    }
                } catch (e: Exception) {
                    logger.error("KpiHelper: 代码Action延迟关闭失败", e)
                }
            }
        } else {
            logger.info("KpiHelper: 自动关闭对话框功能已禁用")
        }
    }

    /**
     * 执行清除窗口操作
     */
    private fun executeCleanupAction() {
        try {
            logger.info("KpiHelper: 执行清除窗口操作")
            val actionManager = ActionManager.getInstance()
            val cleanupAction = actionManager.getAction("codegpt.清除窗口")

            if (cleanupAction != null) {
                val dataContext = getCurrentEditorDataContext() ?: SimpleDataContext.getProjectContext(project)
                val event = AnActionEvent.createFromAnAction(cleanupAction, null, "KpiHelper.Cleanup", dataContext)

                cleanupAction.actionPerformed(event)
                logger.info("KpiHelper: 成功执行清除窗口操作")
            } else {
                logger.warn("KpiHelper: 未找到codegpt.清除窗口 Action")
            }
        } catch (e: Exception) {
            logger.error("KpiHelper: 执行清除窗口操作失败", e)
        }
    }

    /**
     * 安排自动关闭对话框（适用于所有Action）
     */
    private fun scheduleAutoCloseDialog() {
        if (config.autoCloseDialogAfterChat) {
            logger.info("KpiHelper: 安排自动关闭对话框，延迟${config.dialogCloseDelaySeconds}秒")
            ApplicationManager.getApplication().executeOnPooledThread {
                try {
                    Thread.sleep(config.dialogCloseDelaySeconds * 1000L)
                    ApplicationManager.getApplication().invokeLater {
                        closeDialogWithShiftEsc()
                    }
                } catch (e: Exception) {
                    logger.error("KpiHelper: 自动关闭对话框失败", e)
                }
            }
        } else {
            logger.info("KpiHelper: 自动关闭对话框功能已禁用")
        }
    }

    /**
     * 使用Shift+ESC关闭对话框（Mac快捷键）
     */
    private fun closeDialogWithShiftEsc() {
        try {
            logger.info("KpiHelper: 准备关闭对话框")
            val robot = Robot()

            if (SystemInfo.isMac) {
                // Mac系统使用Shift+ESC
                robot.keyPress(KeyEvent.VK_SHIFT)
                robot.keyPress(KeyEvent.VK_ESCAPE)
                Thread.sleep(100)
                robot.keyRelease(KeyEvent.VK_ESCAPE)
                robot.keyRelease(KeyEvent.VK_SHIFT)
                logger.info("KpiHelper: 已发送Shift+ESC关闭对话框")
            } else {
                // Windows/Linux系统使用ESC
                robot.keyPress(KeyEvent.VK_ESCAPE)
                Thread.sleep(100)
                robot.keyRelease(KeyEvent.VK_ESCAPE)
                logger.info("KpiHelper: 已发送ESC关闭对话框")
            }

        } catch (e: Exception) {
            logger.error("KpiHelper: 关闭对话框失败", e)
        }
    }
}

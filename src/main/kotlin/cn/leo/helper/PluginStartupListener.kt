package cn.leo.helper

import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.project.Project
import com.intellij.openapi.project.ProjectManagerListener
import com.intellij.openapi.diagnostic.Logger
import java.util.concurrent.Executors
import java.util.concurrent.ScheduledExecutorService
import java.util.concurrent.TimeUnit

class PluginStartupListener : ProjectManagerListener {

    private var scheduler: ScheduledExecutorService? = null
    private var currentProject: Project? = null
    private val logger = Logger.getInstance(PluginStartupListener::class.java)
    private val config = KpiHelperConfig.getInstance()

    override fun projectOpened(project: Project) {
        if (scheduler != null && !scheduler!!.isShutdown) return

        currentProject = project

        // 检查是否启用
        if (!config.isEnabled()) {
            logger.info("KpiHelper: 插件已禁用，跳过启动")
            return
        }

        logger.info("KpiHelper: 项目已打开: ${project.name}，准备延迟初始化")

        // 延迟初始化，避免过早访问IDE服务
        ApplicationManager.getApplication().executeOnPooledThread {
            try {
                // 等待IDE完全初始化（进一步增加延迟时间）
                Thread.sleep(15000) // 等待15秒
                
                ApplicationManager.getApplication().invokeLater {
                    initializeScheduler(project)
                }
            } catch (e: Exception) {
                logger.error("KpiHelper: 延迟初始化失败", e)
            }
        }
    }

    override fun projectClosed(project: Project) {
        if (project == currentProject) {
            logger.info("KpiHelper: 项目已关闭: ${project.name}")
            currentProject = null
            
            scheduler?.shutdown()
            scheduler = null
        }
    }

    private fun initializeScheduler(project: Project) {
        try {
            logger.info("KpiHelper: 开始初始化调度器")
            logger.info("KpiHelper: 当前配置 - enabled: ${config.enabled}, initialDelay: ${config.initialDelaySeconds}秒")
            
            if (scheduler != null && !scheduler!!.isShutdown) {
                logger.info("KpiHelper: 调度器已存在，跳过初始化")
                return
            }
            
            scheduler = Executors.newSingleThreadScheduledExecutor { r ->
                Thread(r, "KpiHelper-Scheduler").apply {
                    isDaemon = true
                }
            }

            // 首次执行
            val initialDelay = config.initialDelaySeconds.toLong()
            logger.info("KpiHelper: 安排首次执行，延迟: ${initialDelay}秒")
            scheduleNextExecution(initialDelay)
            logger.info("KpiHelper: AI助手自动化插件已启动，项目: ${project.name}")
            
        } catch (e: Exception) {
            logger.error("KpiHelper: 初始化调度器失败", e)
        }
    }

    private fun scheduleNextExecution(delaySeconds: Long) {
        val task = Runnable {
            try {
                logger.info("KpiHelper: 定时任务触发，开始检查执行条件")
                
                // 检查是否仍然启用
                if (!config.isEnabled()) {
                    logger.info("KpiHelper: 插件已禁用，停止执行")
                    return@Runnable
                }
                logger.info("KpiHelper: 插件已启用，继续检查")

                // 检查是否在工作时间（添加详细调试信息）
                val inWorkingHours = config.isInWorkingHours()
                logger.info("KpiHelper: 工作时间检查结果: $inWorkingHours")
                logger.info("KpiHelper: 启用工作时间限制: ${config.enableWorkingHoursOnly}")
                logger.info("KpiHelper: 启用工作日限制: ${config.enableWeekdaysOnly}")
                
                if (!inWorkingHours) {
                    val calendar = java.util.Calendar.getInstance()
                    val hour = calendar.get(java.util.Calendar.HOUR_OF_DAY)
                    val dayOfWeek = calendar.get(java.util.Calendar.DAY_OF_WEEK)
                    logger.info("KpiHelper: 当前时间: ${hour}时, 星期: $dayOfWeek")
                    logger.info("KpiHelper: 工作时间: ${config.workingHoursStart}-${config.workingHoursEnd}")
                    logger.info("KpiHelper: 当前不在工作时间，跳过执行")
                    // 安排下次检查（较短间隔）
                    scheduleNextExecution(1800) // 30分钟 = 1800秒后再检查
                    return@Runnable
                }
                logger.info("KpiHelper: 在工作时间内，开始执行AI交互")

                // 检查是否需要用户确认
                if (config.requireUserConfirmation) {
                    logger.info("KpiHelper: 需要用户确认，显示确认对话框")
                    ApplicationManager.getApplication().invokeLater {
                        showScheduledExecutionConfirmation()
                    }
                } else {
                    logger.info("KpiHelper: 无需用户确认，直接执行")
                    executeAIInteraction()
                }
            } catch (e: Exception) {
                logger.error("KpiHelper: 执行AI交互时发生错误", e)
                config.incrementFailureCount()
            } finally {
                // 检查是否仍然启用，如果启用才安排下次执行
                if (config.isEnabled()) {
                    val nextDelay = config.getRandomInterval()
                    logger.info("KpiHelper: 安排下次执行，间隔: $nextDelay 秒")
                    scheduleNextExecution(nextDelay)
                } else {
                    logger.info("KpiHelper: 插件已禁用，停止安排下次执行")
                }
            }
        }

        if (scheduler == null || scheduler!!.isShutdown) {
            logger.warn("KpiHelper: 调度器不可用，无法安排任务")
            return
        }

        scheduler?.schedule(task, delaySeconds, TimeUnit.SECONDS)
        logger.info("KpiHelper: 已安排下次AI交互，将在 $delaySeconds 秒后执行")
    }

    /**
     * 显示定时执行确认对话框
     */
    private fun showScheduledExecutionConfirmation() {
        val project = currentProject ?: return
        
        val result = com.intellij.openapi.ui.Messages.showYesNoDialog(
            project,
            "AI助手自动化插件准备执行定时任务。\n\n是否继续执行？",
            "定时执行确认",
            "执行",
            "跳过",
            com.intellij.openapi.ui.Messages.getQuestionIcon()
        )
        
        if (result == com.intellij.openapi.ui.Messages.YES) {
            logger.info("KpiHelper: 用户确认执行定时任务")
            executeAIInteraction()
        } else {
            logger.info("KpiHelper: 用户取消执行定时任务")
        }
    }

    /**
     * 执行AI交互（直接调用Tool Window交互逻辑）
     */
    private fun executeAIInteraction() {
        val project = currentProject ?: return

        try {
            logger.info("KpiHelper: 开始执行AI交互（使用Tool Window交互逻辑）")
            config.incrementInteractionCount()

            val helper = YTOAIPluginHelper()
            val interactor = YTOAIInteractor(project)
            
            // 获取用户自定义的第一个action，如果没有则使用NewChat
            val selectedAction = if (config.customAIActionIds.isNotEmpty()) {
                config.customAIActionIds.first()
            } else {
                "YtoAICode.NewChat"
            }
            
            // 根据Action类型生成合适的问题
            val generator = AIQuestionGenerator()
            val testQuestion = if (helper.isChatAction(selectedAction)) {
                generator.generateQuestion()
            } else {
                null
            }
            
            logger.info("KpiHelper: 选择执行Action: $selectedAction")
            if (!testQuestion.isNullOrEmpty()) {
                logger.info("KpiHelper: 生成的问题: $testQuestion")
            }
            
            val success = interactor.executeSpecificAction(selectedAction, testQuestion)
            
            if (success) {
                config.incrementSuccessCount()
                logger.info("KpiHelper: AI交互执行成功")
            } else {
                config.incrementFailureCount()
                logger.warn("KpiHelper: AI交互执行失败")
            }
            
        } catch (e: Exception) {
            logger.error("KpiHelper: 执行AI交互时发生异常", e)
            config.incrementFailureCount()
        }
    }
}
